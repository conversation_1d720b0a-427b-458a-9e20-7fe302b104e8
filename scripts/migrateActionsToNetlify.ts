import fs from 'fs/promises';
import path from 'path';
import fg from 'fast-glob';

// Convert kebab-case or snake_case to camelCase
function toCamelCase(str: string): string {
  return str
    .replace(/[-_](.)/g, (_, char) => char.toUpperCase())
    .replace(/^./, str => str.toLowerCase());
}

async function migrateActions() {
  const sourceDir = path.join('packages', 'nango-integrations');
  const targetDir = path.join('packages', 'ma-next', 'netlify', 'functions', '_tools', 'actions');

  try {
    // Step 1: Move action files and update imports
    const actionFiles = await fg(path.join(sourceDir, '*', 'actions', '*.ts'));

    if (actionFiles.length === 0) {
      console.log('No action files found in', path.join(sourceDir, '*', 'actions'));
    }

    for (const file of actionFiles) {
      const relativePath = path.relative(sourceDir, file);
      const [providerKey, , actionKeyWithExt] = relativePath.split(path.sep);
      const actionKey = actionKeyWithExt.replace('.ts', '');

      const targetFile = path.join(targetDir, providerKey, `${actionKey}.ts`);
      await fs.mkdir(path.dirname(targetFile), { recursive: true });

      // Read, modify, and write file
      let content = await fs.readFile(file, 'utf-8');
      // Replace all instances of ../../models with ../models (no .ts extension)
      content = content.replace(/\.\.\/\.\.\/models/g, '../models');
      // Replace await nango.log(...) with console.log(...), handling multiline cases
      content = content.replace(/await\s+nango\.log\([\s\S]*?\)/g, function (match) {
        const logContent = match.slice(16, -1).trim();
        return `console.error(${logContent})`;
      });
      // Replace ../utils imports with ./utils
      content = content.replace(/\.\.\/utils/g, './utils');
      await fs.writeFile(targetFile, content);

      console.log(`Moved and updated: ${file} -> ${targetFile}`);
    }

    // Step 2: Move utils folders if they exist
    const providerDirs = (await fs.readdir(sourceDir, { withFileTypes: true }))
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    for (const providerKey of providerDirs) {
      const sourceUtilsDir = path.join(sourceDir, providerKey, 'utils');
      try {
        await fs.access(sourceUtilsDir);
        const targetUtilsDir = path.join(targetDir, providerKey, 'utils');
        await fs.mkdir(path.dirname(targetUtilsDir), { recursive: true });

        // Copy the entire utils directory
        await copyDirectory(sourceUtilsDir, targetUtilsDir);
        console.log(`Moved utils: ${sourceUtilsDir} -> ${targetUtilsDir}`);
      } catch (err) {
        // Utils folder doesn't exist for this provider, skip
      }
    }

    // Step 3: Move models.ts
    const sourceModels = path.join(sourceDir, 'models.ts');
    const targetModels = path.join(targetDir, 'models.ts');
    try {
      await fs.access(sourceModels); // Check if sourceModels exists
      await fs.copyFile(sourceModels, targetModels);
      console.log(`Moved: ${sourceModels} -> ${targetModels}`);
    } catch (err) {
      console.warn(`Warning: ${sourceModels} not found, skipping models.ts move.`);
    }

    // Step 4: Generate index.ts
    const indexContent = await generateIndexContent(targetDir);
    const indexPath = path.join(targetDir, 'index.ts');
    await fs.writeFile(indexPath, indexContent);
    console.log(`Created: ${indexPath}`);
  } catch (err) {
    throw new Error(`Migration failed: ${err.message}`);
  }
}

async function copyDirectory(src: string, dest: string): Promise<void> {
  await fs.mkdir(dest, { recursive: true });
  const entries = await fs.readdir(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.copyFile(srcPath, destPath);
    }
  }
}

async function generateIndexContent(targetDir: string): Promise<string> {
  const providerDirs = (await fs.readdir(targetDir, { withFileTypes: true }))
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name);

  let imports = '';
  let lookupMap = 'const actionMap: Record<string, Function> = {\n';

  for (const providerKey of providerDirs) {
    const actionFiles = await fg(path.join(targetDir, providerKey, '*.ts'));

    for (const file of actionFiles) {
      const actionKey = path.basename(file, '.ts');
      // Skip index, models, and any files in utils subfolder
      if (
        actionKey === 'index' ||
        actionKey === 'models' ||
        file.includes('/utils/')
      ) {
        continue;
      }

      const importName = toCamelCase(`${providerKey}_${actionKey}`);
      // Import without .ts extension
      const importPath = `./${providerKey}/${actionKey}`;

      imports += `import ${importName} from '${importPath}';\n`;
      lookupMap += `  ['${providerKey}:${actionKey}']: ${importName},\n`;
    }
  }

  lookupMap += '};\n';

  const getRunnerFunction = `
export function getRunner(providerKey: string, actionKey: string): Function | undefined {
  return actionMap[\`\${providerKey}:\${actionKey}\`];
}
`;

  return `// Generated index file for action lookup
${imports}
${lookupMap}
${getRunnerFunction}
`;
}

migrateActions().catch(err => {
  console.error('Error during migration:', err);
  process.exit(1);
});
