{"data": [{"name": "add-historical-time-entry", "description": "Adds a completed time entry for a specific duration or start/end time. Checks company settings for duration vs timestamp tracking.", "parameters": {"type": "object", "properties": {"project_id": {"type": "number"}, "task_id": {"type": "number"}, "spent_date": {"type": "string"}, "hours": {"type": "number"}, "started_time": {"type": "string"}, "ended_time": {"type": "string"}, "notes": {"type": "string"}, "user_id": {"type": "number"}, "external_reference": {"$ref": "#/definitions/HarvestExternalReferenceInput"}}, "required": ["project_id", "task_id", "spent_date"]}}, {"name": "create-client", "description": "Creates a new client in Harvest.", "parameters": {"type": "object", "properties": {"name": {"type": "string"}, "is_active": {"type": "boolean"}, "address": {"type": ["string", "null"]}, "currency": {"type": ["string", "null"]}}, "required": ["name"]}}, {"name": "create-project", "description": "Creates a new project in Harvest.", "parameters": {"type": "object", "properties": {"client_id": {"type": "number"}, "name": {"type": "string"}, "is_billable": {"type": "boolean"}, "bill_by": {"type": "string"}, "budget_by": {"type": "string"}, "is_fixed_fee": {"type": "boolean"}, "fee": {"type": ["number", "null"]}, "hourly_rate": {"type": ["number", "null"]}, "budget": {"type": ["number", "null"]}, "budget_is_monthly": {"type": "boolean"}, "notify_when_over_budget": {"type": "boolean"}, "over_budget_notification_percentage": {"type": "number"}, "show_budget_to_all": {"type": "boolean"}, "cost_budget": {"type": ["number", "null"]}, "cost_budget_include_expenses": {"type": "boolean"}, "notes": {"type": ["string", "null"]}, "starts_on": {"type": ["string", "null"]}, "ends_on": {"type": ["string", "null"]}}, "required": ["client_id", "name", "is_billable", "bill_by", "budget_by"]}}, {"name": "delete-project", "description": "Deletes a project in Harvest.", "parameters": {"type": "object", "properties": {"project_id": {"type": "number"}}, "required": ["project_id"]}}, {"name": "delete-time-entry", "description": "Deletes a time entry in Harvest.", "parameters": {"type": "object", "properties": {"timeEntryId": {"type": "number"}, "id": {"type": "number"}}, "required": ["timeEntryId"]}}, {"name": "get-client", "description": "Gets a specific client by ID.", "parameters": {"type": "object", "properties": {"client_id": {"type": "number"}}, "required": ["client_id"]}}, {"name": "get-project", "description": "Gets a specific project by ID.", "parameters": {"type": "object", "properties": {"project_id": {"type": "number"}}, "required": ["project_id"]}}, {"name": "get-time-entry", "description": "Gets a specific time entry by ID.", "parameters": {"type": "object", "properties": {"timeEntryId": {"type": "number"}, "id": {"type": "number"}}, "required": ["timeEntryId"]}}, {"name": "list-clients", "description": "Lists clients from Harvest.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list-projects", "description": "Lists projects from Harvest.", "parameters": {"type": "object", "properties": {"client_id": {"type": "number"}, "is_active": {"type": "boolean"}, "page": {"type": "number"}, "per_page": {"type": "number"}}, "required": []}}, {"name": "list-project-tasks", "description": "Lists task assignments for a specific project in Harvest.", "parameters": {"type": "object", "properties": {"project_id": {"type": "number"}}, "required": ["project_id"]}}, {"name": "list-tasks", "description": "Lists tasks from Harvest.", "parameters": {"type": "object", "properties": {"is_active": {"type": "boolean"}, "updated_since": {"type": "string"}, "page": {"type": "number"}, "per_page": {"type": "number"}}, "required": []}}, {"name": "list-time-entries", "description": "Lists time entries from Harvest.", "parameters": {"type": "object", "properties": {"userId": {"type": "number"}, "clientId": {"type": "number"}, "projectId": {"type": "number"}, "taskId": {"type": "number"}, "from": {"type": "string"}, "to": {"type": "string"}, "page": {"type": "number"}, "perPage": {"type": "number"}}, "required": []}}, {"name": "restart-timer", "description": "Restarts a stopped time entry in Harvest.", "parameters": {"type": "object", "properties": {"timeEntryId": {"type": "number"}, "id": {"type": "number"}}, "required": ["timeEntryId"]}}, {"name": "start-timer", "description": "Starts a new timer for a task. Checks company settings for duration vs timestamp tracking.", "parameters": {"type": "object", "properties": {"project_id": {"type": "number"}, "task_id": {"type": "number"}, "spent_date": {"type": "string"}, "started_time": {"type": "string"}, "notes": {"type": "string"}, "user_id": {"type": "number"}, "external_reference": {"$ref": "#/definitions/HarvestExternalReferenceInput"}}, "required": ["project_id", "task_id", "spent_date"]}}, {"name": "stop-timer", "description": "Stops a running time entry in Harvest.", "parameters": {"type": "object", "properties": {"timeEntryId": {"type": "number"}, "id": {"type": "number"}}, "required": ["timeEntryId"]}}, {"name": "update-time-entry", "description": "Updates an existing time entry in Harvest.", "parameters": {"type": "object", "properties": {"time_entry_id": {"type": "number"}, "project_id": {"type": "number"}, "task_id": {"type": "number"}, "spent_date": {"type": "string"}, "hours": {"type": "number"}, "started_time": {"type": "string"}, "ended_time": {"type": "string"}, "notes": {"type": "string"}, "external_reference": {"$ref": "#/definitions/HarvestExternalReferenceInput"}}, "required": ["time_entry_id"]}}, {"name": "issues", "description": "Fetches Github issues from all a user's repositories", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list-files", "description": "Lists all the files of a Github repo given a specific branch", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "add-pull-request-review-comment", "description": "Add a review comment to a pull request.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "pull_number": {"type": "number"}, "body": {"type": "string"}, "commit_id": {"type": "string"}, "path": {"type": "string"}, "subject_type": {"type": "string"}, "line": {"type": "number"}, "side": {"type": "string"}, "start_line": {"type": "number"}, "start_side": {"type": "string"}, "in_reply_to": {"type": "number"}, "diff_hunk": {"type": "string"}}, "required": ["owner", "repo", "pull_number", "body"]}}, {"name": "create-issue", "description": "Creates a new issue in a repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}, "assignees": {"type": "array", "items": {"type": "string"}}, "labels": {"type": "array", "items": {"type": "string"}}}, "required": ["owner", "repo", "title"]}}, {"name": "create-organization-repository", "description": "Creates a new repository within a specified organization.", "parameters": {"type": "object", "properties": {"org": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "homepage": {"type": "string"}, "private": {"type": "boolean"}, "has_issues": {"type": "boolean"}, "has_projects": {"type": "boolean"}, "has_wiki": {"type": "boolean"}}, "required": ["org", "name"]}}, {"name": "create-pull-request", "description": "Create a new pull request in a GitHub repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "title": {"type": "string"}, "body": {"type": "string"}, "head": {"type": "string"}, "base": {"type": "string"}, "draft": {"type": "boolean"}, "maintainer_can_modify": {"type": "boolean"}}, "required": ["owner", "repo", "title", "head", "base"]}}, {"name": "create-pull-request-review", "description": "Submit a review on a pull request.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "pullNumber": {"type": "number"}, "body": {"type": "string"}, "event": {"type": "string"}, "commitId": {"type": "string"}, "comments": {"type": "array", "items": {"$ref": "#/definitions/GithubDraftReviewComment"}}}, "required": ["owner", "repo", "pullNumber", "event"]}}, {"name": "create-repository", "description": "Creates a new repository for the authenticated user. After successful creation, describe to the user how they can push to it inluding ssh url.", "parameters": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "private": {"type": "boolean"}, "has_issues": {"type": "boolean"}, "has_projects": {"type": "boolean"}, "has_wiki": {"type": "boolean"}, "auto_init": {"type": "boolean"}, "gitignore_template": {"type": "string"}, "license_template": {"type": "string"}}, "required": ["name"]}}, {"name": "delete-repository", "description": "Deletes a repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}}, "required": ["owner", "repo"]}}, {"name": "get-issue", "description": "Gets a specific issue by number.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "issue_number": {"type": "number"}}, "required": ["owner", "repo", "issue_number"]}}, {"name": "get-pull-request", "description": "Get details of a specific pull request in a GitHub repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "pullNumber": {"type": "number"}}, "required": ["owner", "repo", "pullNumber"]}}, {"name": "get-pull-request-comments", "description": "Get the review comments on a pull request.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "pullNumber": {"type": "number"}}, "required": ["owner", "repo", "pullNumber"]}}, {"name": "get-pull-request-files", "description": "Get the files changed in a specific pull request.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "pullNumber": {"type": "number"}}, "required": ["owner", "repo", "pullNumber"]}}, {"name": "get-pull-request-status", "description": "Get the combined status of all status checks for a pull request.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "pullNumber": {"type": "number"}}, "required": ["owner", "repo", "pullNumber"]}}, {"name": "get-repository", "description": "Gets a specific repository by owner and name.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}}, "required": ["owner", "repo"]}}, {"name": "list-branches", "description": "List branches in a repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "protected": {"type": "boolean"}, "per_page": {"type": "number"}, "page": {"type": "number"}}, "required": ["owner", "repo"]}}, {"name": "list-issues", "description": "Lists issues for a repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "state": {"type": "string"}, "sort": {"type": "string"}, "direction": {"type": "string"}, "per_page": {"type": "number"}, "page": {"type": "number"}}, "required": ["owner", "repo"]}}, {"name": "list-pull-requests", "description": "List pull requests in a GitHub repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "state": {"type": "string"}, "head": {"type": "string"}, "base": {"type": "string"}, "sort": {"type": "string"}, "direction": {"type": "string"}, "per_page": {"type": "number"}, "page": {"type": "number"}}, "required": ["owner", "repo"]}}, {"name": "list-repos", "description": "List github repos from an organization.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list-repositories", "description": "Lists repositories for the authenticated user.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "merge-pull-request", "description": "Merge a pull request in a GitHub repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "pullNumber": {"type": "number"}, "commit_title": {"type": "string"}, "commit_message": {"type": "string"}, "merge_method": {"type": "string"}}, "required": ["owner", "repo", "pullNumber"]}}, {"name": "update-issue", "description": "Updates an existing issue.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "issue_number": {"type": "number"}, "title": {"type": "string"}, "body": {"type": "string"}, "state": {"type": "string"}, "assignees": {"type": "array", "items": {"type": "string"}}, "labels": {"type": "array", "items": {"type": "string"}}}, "required": ["owner", "repo", "issue_number"]}}, {"name": "update-pull-request", "description": "Update an existing pull request in a GitHub repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "pullNumber": {"type": "number"}, "title": {"type": "string"}, "body": {"type": "string"}, "state": {"type": "string"}, "base": {"type": "string"}, "maintainer_can_modify": {"type": "boolean"}}, "required": ["owner", "repo", "pullNumber"]}}, {"name": "update-pull-request-branch", "description": "Update the branch of a pull request with the latest changes from the base branch.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "pullNumber": {"type": "number"}, "expectedHeadSha": {"type": "string"}}, "required": ["owner", "repo", "pullNumber"]}}, {"name": "update-repository", "description": "Updates an existing repository.", "parameters": {"type": "object", "properties": {"owner": {"type": "string"}, "repo": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "private": {"type": "boolean"}, "has_issues": {"type": "boolean"}, "has_projects": {"type": "boolean"}, "has_wiki": {"type": "boolean"}, "default_branch": {"type": "string"}}, "required": ["owner", "repo"]}}, {"name": "write-file", "description": "Write content to a particular github file within a repo. If\nthe file doesn't exist it creates and then writes to it", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "messages", "description": "Syncs messages from all channels the user can access, including replies.\nCursors are stored per channel in metadata for incremental syncing.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "add-reaction-as-user", "description": "Adds an emoji reaction to a message as the authenticated user.", "parameters": {"type": "object", "properties": {"name": {"type": "string"}, "channel": {"type": "string"}, "timestamp": {"type": "string"}}, "required": ["name", "channel", "timestamp"]}}, {"name": "get-channel-history", "description": "Retrieves message history for a specific channel.", "parameters": {"type": "object", "properties": {"channel": {"type": "string"}, "limit": {"type": "number"}, "latest": {"type": "string"}, "oldest": {"type": "string"}, "cursor": {"type": "string"}}, "required": ["channel"]}}, {"name": "get-message-permalink", "description": "Retrieves a permalink for a specific message.", "parameters": {"type": "object", "properties": {"channel": {"type": "string"}, "message_ts": {"type": "string"}}, "required": ["channel", "message_ts"]}}, {"name": "get-user-info", "description": "Retrieves information about a specific user.", "parameters": {"type": "object", "properties": {"user": {"type": "string"}}, "required": ["user"]}}, {"name": "list-channels", "description": "Lists channels in Slack.", "parameters": {"type": "object", "properties": {"types": {"type": "string"}, "limit": {"type": "number"}, "cursor": {"type": "string"}}, "required": []}}, {"name": "search-messages", "description": "Searches for messages matching a query.", "parameters": {"type": "object", "properties": {"query": {"type": "string"}, "sort": {"type": "string"}, "sort_dir": {"type": "string"}, "count": {"type": "number"}, "page": {"type": "number"}}, "required": ["query"]}}, {"name": "send-message-as-user", "description": "Sends a message to a Slack channel as the authenticated user.", "parameters": {"type": "object", "properties": {"channel": {"type": "string"}, "text": {"type": "string"}, "thread_ts": {"type": "string"}}, "required": ["channel", "text"]}}, {"name": "update-message-as-user", "description": "Updates an existing message in a channel as the authenticated user.", "parameters": {"type": "object", "properties": {"channel": {"type": "string"}, "ts": {"type": "string"}, "text": {"type": "string"}}, "required": ["channel", "ts", "text"]}}, {"name": "calendars", "description": "Sync the calendars list of the user", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "events", "description": "Sync calendar events on the primary calendar going back one month and\nsave the entire object as specified by the Google API", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "create-event", "description": "Creates a new event in Google Calendar. Can either be full-day or time-based.\n- summary: Event title / name.\n- description: Contains e.g. the agenda or specifics of the meeting. Can contain HTML.\n- location: Free form text.\n- start: Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.\n- end: Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.\n- timeZone: An IANA Time Zone e.g. (Area/City)\n- attendees: A list of attendee email addresses.", "parameters": {"type": "object", "properties": {"summary": {"type": "string", "description": "Event title / name."}, "description": {"type": "string", "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML."}, "location": {"type": "string", "description": "Free form text."}, "start": {"type": "string", "description": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."}, "end": {"type": "string", "description": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."}, "timeZone": {"type": "string", "description": "An IANA Time Zone e.g. (Area/City)"}, "attendees": {"type": "array", "items": {"type": "string"}, "description": "A list of attendee email addresses."}}, "required": ["summary", "start", "end"]}}, {"name": "delete-event", "description": "Deletes an event from Google Calendar.\n- calendarId: Calendar identifier. Use \"primary\" unless otherwise advised.\n- eventId: Event identifier.\n- sendUpdates: Whether to send notifications about the deletion of the event.", "parameters": {"type": "object", "properties": {"calendarId": {"type": "string", "description": "Calendar identifier. Use \"primary\" unless otherwise advised."}, "eventId": {"type": "string", "description": "Event identifier."}, "sendUpdates": {"type": "string", "description": "Whether to send notifications about the deletion of the event."}}, "required": ["calendarId", "eventId"]}}, {"name": "list-calendars", "description": "Lists all calendars available to the authenticated user.\nAVOID USING THIS ENDPOINT; Normally you can just pass \"primary\" as the calendarId to other endpoints, unless explicitly requested otherwise.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list-events", "description": "Lists events from a specified calendar. By default will only include future events. To include past events, set the timeMin to some time in the past.\n- calendarId: Calendar identifier. Use \"primary\" unless otherwise advised.\n- timeMin: Lower bound (inclusive) for an event's end time to filter by. Defaults to now. ISO8601 string format.\n- timeMax: Upper bound (exclusive) for an event's start time to filter by. Defaults to unbounded. ISO8601 string format.\n- maxResults: Defaults to 250. Max 2500.\n- pageToken: Token as per a previous response to get another page of results.\n- q: Free text search terms to find events that match these terms.\n- timeZone: Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar.", "parameters": {"type": "object", "properties": {"calendarId": {"type": "string", "description": "Calendar identifier. Use \"primary\" unless otherwise advised."}, "timeMin": {"type": "string", "description": "Lower bound (inclusive) for an event's end time to filter by. Defaults to now. ISO8601 string format."}, "timeMax": {"type": "string", "description": "Upper bound (exclusive) for an event's start time to filter by. Defaults to unbounded. ISO8601 string format."}, "maxResults": {"type": "number", "description": "Defaults to 250. Max 2500."}, "pageToken": {"type": "string", "description": "Token as per a previous response to get another page of results."}, "orderBy": {"type": "string"}, "q": {"type": "string", "description": "Free text search terms to find events that match these terms."}, "singleEvents": {"type": "boolean"}, "timeZone": {"type": "string", "description": "Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar."}}, "required": ["calendarId"]}}, {"name": "settings", "description": "Fetch all user settings from Google Calendar", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update-event", "description": "Updates an event in Google Calendar.\n- calendarId: Calendar identifier. Use \"primary\" unless otherwise advised.\n- eventId: Event identifier.\n- sendUpdates: Whether to send notifications about the event update.\n- summary: Event title / name.\n- description: Contains e.g. the agenda or specifics of the meeting. Can contain HTML.\n- location: Free form text.\n- start: Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.\n- end: Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.\n- timeZone: An IANA Time Zone e.g. (Area/City)\n- attendees: A list of attendee email addresses.", "parameters": {"type": "object", "properties": {"calendarId": {"type": "string", "description": "Calendar identifier. Use \"primary\" unless otherwise advised."}, "eventId": {"type": "string", "description": "Event identifier."}, "sendUpdates": {"type": "string", "description": "Whether to send notifications about the event update."}, "summary": {"type": "string", "description": "Event title / name."}, "description": {"type": "string", "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML."}, "location": {"type": "string", "description": "Free form text."}, "start": {"type": "string", "description": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter."}, "end": {"type": "string", "description": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter."}, "timeZone": {"type": "string", "description": "An IANA Time Zone e.g. (Area/City)"}, "attendees": {"type": "array", "items": {"$ref": "#/definitions/GoogleCalendarAttendeeInput"}, "description": "A list of attendee email addresses."}}, "required": ["calendarId", "eventId"]}}, {"name": "whoami", "description": "description: Fetch current user information", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "emails", "description": "Fetches a list of emails from gmail. Goes back default to 1 year\nbut metadata can be set using the `backfillPeriodMs` property\nto change the lookback. The property should be set in milliseconds.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "emails-fork", "description": "Fetches a list of emails from Gmail. Defaults to 1-day backfill,\nadjustable via `backfillPeriodMs` in milliseconds.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "emails-labels-added", "description": "Tracks emails where labels have been added using Gmail History API.\nStores historyId in metadata for incremental syncing.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "labels", "description": "Fetches a list of labels from gmail.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "compose-draft", "description": "Creates a new draft email in Gmail.", "parameters": {"type": "object", "properties": {"recipient": {"type": "string"}, "subject": {"type": "string"}, "body": {"type": "string"}, "headers": {"type": "object"}}, "required": ["recipient", "subject"]}}, {"name": "compose-draft-reply", "description": "Creates a new draft email that is a reply to an existing email.", "parameters": {"type": "object", "properties": {"sender": {"type": "string"}, "subject": {"type": "string"}, "body": {"type": "string"}, "threadId": {"type": "string"}, "messageId": {"type": "string"}, "inReplyTo": {"type": "string"}, "references": {"type": "string"}, "date": {"type": "string"}, "replyBody": {"type": "string"}}, "required": ["sender", "subject", "body", "threadId", "messageId", "inReplyTo", "references", "date", "replyBody"]}}, {"name": "delete-message", "description": "Permanently deletes the specified message. Bypasses Trash.", "parameters": {"type": "object", "properties": {"messageId": {"type": "string"}}, "required": ["messageId"]}}, {"name": "get-message", "description": "Retrieves a specific email by <PERSON>.", "parameters": {"type": "object", "properties": {"id": {"type": "string"}, "format": {"type": "string"}}, "required": ["id"]}}, {"name": "list-messages", "description": "Lists emails from Gmail inbox with optional filtering.", "parameters": {"type": "object", "properties": {"maxResults": {"type": "number"}, "labelIds": {"type": "array", "items": {"type": "string"}}, "q": {"type": "string"}, "pageToken": {"type": "string"}}, "required": []}}, {"name": "modify-message-labels", "description": "Modifies the labels applied to a specific message.", "parameters": {"type": "object", "properties": {"messageId": {"type": "string"}, "addLabelIds": {"type": "array", "items": {"type": "string"}}, "removeLabelIds": {"type": "array", "items": {"type": "string"}}}, "required": ["messageId"]}}, {"name": "send-email", "description": "Sends an email via Gmail.", "parameters": {"type": "object", "properties": {"to": {"type": "string"}, "subject": {"type": "string"}, "body": {"type": "string"}, "from": {"type": "string"}, "cc": {"type": "string"}, "bcc": {"type": "string"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/GmailAttachment"}}}, "required": ["to", "subject", "body"]}}, {"name": "trash-message", "description": "Moves the specified message to the trash.", "parameters": {"type": "object", "properties": {"messageId": {"type": "string"}}, "required": ["messageId"]}}, {"name": "untrash-message", "description": "Removes the specified message from the trash.", "parameters": {"type": "object", "properties": {"messageId": {"type": "string"}}, "required": ["messageId"]}}, {"name": "files-fork", "description": "Fetches files metadata from Dropbox.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "copy-file", "description": "Copy a file or folder to a different location in Dropbox and return metadata of the copied item", "parameters": {"type": "object", "properties": {"from_path": {"type": "string"}, "to_path": {"type": "string"}, "allow_shared_folder": {"type": "boolean"}, "autorename": {"type": "boolean"}}, "required": ["from_path", "to_path"]}}, {"name": "create-folder", "description": "Create a new folder in Dropbox and return folder metadata", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "autorename": {"type": "boolean"}}, "required": ["path"]}}, {"name": "create-user", "description": "Creates a user in Dropbox. Requires Dropbox Business.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete-file", "description": "Delete a file or folder in Dropbox and return metadata of the deleted item", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"]}}, {"name": "fetch-file", "description": "Fetches the content of a file given its ID, processes the data using a response stream, and encodes it into a base64 string. This base64-encoded string can be used to recreate the file in its original format using an external tool.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get-file", "description": "Get file metadata and a download URL (not the actual file content)", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"]}}, {"name": "list-files", "description": "List files and folders in a Dropbox directory", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "recursive": {"type": "boolean"}, "limit": {"type": "number"}, "include_deleted": {"type": "boolean"}}, "required": ["path"]}}, {"name": "move-file", "description": "Move a file or folder to a different location in Dropbox and return metadata of the moved item", "parameters": {"type": "object", "properties": {"from_path": {"type": "string"}, "to_path": {"type": "string"}, "allow_shared_folder": {"type": "boolean"}, "autorename": {"type": "boolean"}, "allow_ownership_transfer": {"type": "boolean"}}, "required": ["from_path", "to_path"]}}, {"name": "search-files", "description": "Search for files and folders in Dropbox by filename or content", "parameters": {"type": "object", "properties": {"query": {"type": "string"}, "path": {"type": "string"}, "max_results": {"type": "number"}, "mode": {"type": "string"}}, "required": ["query"]}}, {"name": "upload-file", "description": "Upload string content as a file to Dropbox", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}, "mode": {"type": "string"}, "autorename": {"type": "boolean"}, "mute": {"type": "boolean"}}, "required": ["path", "content"]}}, {"name": "create-database", "description": "Creates a new Notion database as a subpage of a specified page.", "parameters": {"type": "object", "properties": {"parentId": {"type": "string"}, "title": {"type": "array", "items": {"$ref": "#/definitions/NotionRichText"}}, "properties": {"type": "object"}}, "required": ["parentId", "title", "properties"]}}, {"name": "create-page", "description": "Creates a new Notion page.", "parameters": {"type": "object", "properties": {"parentId": {"type": "string"}, "parentType": {"type": "string"}, "properties": {"type": "object"}, "children": {"type": "array", "items": {"type": "object"}}}, "required": ["parentId", "properties"]}}, {"name": "get-database", "description": "Retrieves a specific Notion Database object by its ID.", "parameters": {"type": "object", "properties": {"databaseId": {"type": "string"}}, "required": ["databaseId"]}}, {"name": "get-page", "description": "Retrieves a specific Notion Page object by its ID.", "parameters": {"type": "object", "properties": {"pageId": {"type": "string"}}, "required": ["pageId"]}}, {"name": "query-database", "description": "Queries a Notion database for pages, with optional filters and sorts.", "parameters": {"type": "object", "properties": {"databaseId": {"type": "string"}, "filter": {"type": "object"}, "sorts": {"type": "array", "items": {"type": "object"}}, "start_cursor": {"type": "string"}, "page_size": {"type": "number"}}, "required": ["databaseId"]}}, {"name": "search", "description": "Searches pages and databases in Notion. IMPORTANT - Use \"\" to search for everything.", "parameters": {"type": "object", "properties": {"query": {"type": "string"}, "sort": {"$ref": "#/definitions/NotionSort"}, "filter": {"$ref": "#/definitions/NotionFilter"}, "start_cursor": {"type": "string"}, "page_size": {"type": "number"}}, "required": []}}, {"name": "update-database", "description": "Updates properties of an existing Notion database. ALSO USED TO \"delete\" a database, set archive to true.", "parameters": {"type": "object", "properties": {"databaseId": {"type": "string"}, "title": {"type": "array", "items": {"$ref": "#/definitions/NotionRichText"}}, "description": {"type": "array", "items": {"$ref": "#/definitions/NotionRichText"}}, "properties": {"type": "object"}, "archived": {"type": "boolean"}}, "required": ["databaseId"]}}, {"name": "update-page", "description": "Updates properties of an existing Notion page. ALSO USED TO \"delete\" a page, set archive to true.", "parameters": {"type": "object", "properties": {"pageId": {"type": "string"}, "properties": {"type": "object"}, "archived": {"type": "boolean"}, "icon": {"anyOf": [{"type": "object"}, {"type": "null"}]}, "cover": {"anyOf": [{"type": "object"}, {"type": "null"}]}}, "required": ["pageId"]}}, {"name": "create-document", "description": "Creates a blank Google Document.", "parameters": {"type": "object", "properties": {"title": {"type": "string"}}, "required": []}}, {"name": "fetch-document", "description": "Fetches the content of a document given its ID.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get-document", "description": "Retrieves a specific Google Document.", "parameters": {"type": "object", "properties": {"documentId": {"type": "string"}}, "required": ["documentId"]}}, {"name": "update-document", "description": "Applies batch updates to a Google Document.", "parameters": {"type": "object", "properties": {"documentId": {"type": "string"}, "requests": {"type": "array", "items": {"type": "object"}}, "writeControl": {"type": "object"}}, "required": ["documentId", "requests"]}}, {"name": "create-issue", "description": "Creates a new issue in Linear.", "parameters": {"type": "object", "properties": {"teamId": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "stateId": {"type": "string"}, "assigneeId": {"type": "string"}, "priority": {"type": "number"}, "projectId": {"type": "string"}, "labelIds": {"type": "array", "items": {"type": "string"}}}, "required": ["teamId", "title"]}}, {"name": "create-project", "description": "Creates a new project in Linear.", "parameters": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "icon": {"type": "string"}, "color": {"type": "string"}, "teamIds": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "teamIds"]}}, {"name": "delete-issue", "description": "Deletes an issue in Linear.", "parameters": {"type": "object", "properties": {"issueId": {"type": "string"}}, "required": ["issueId"]}}, {"name": "fetch-models", "description": "Introspection endpoint to fetch the models available", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get-issue", "description": "Gets a specific issue by ID.", "parameters": {"type": "object", "properties": {"issueId": {"type": "string"}}, "required": ["issueId"]}}, {"name": "get-project", "description": "Gets a specific project by ID.", "parameters": {"type": "object", "properties": {"projectId": {"type": "string"}}, "required": ["projectId"]}}, {"name": "get-team", "description": "Gets a specific team by ID.", "parameters": {"type": "object", "properties": {"teamId": {"type": "string"}}, "required": ["teamId"]}}, {"name": "list-issues", "description": "Lists issues from Linear.", "parameters": {"type": "object", "properties": {"teamId": {"type": "string"}, "projectId": {"type": "string"}, "states": {"type": "array", "items": {"type": "string"}}, "assigneeId": {"type": "string"}, "priority": {"type": "number"}, "sortBy": {"type": "string"}, "sortOrder": {"type": "string"}, "limit": {"type": "number"}, "first": {"type": "number"}, "after": {"type": "string"}}, "required": []}}, {"name": "list-projects", "description": "List all projects from Linear", "parameters": {"type": "object", "properties": {"first": {"type": "number"}, "after": {"type": "string"}}, "required": []}}, {"name": "list-teams", "description": "Lists teams from Linear.", "parameters": {"type": "object", "properties": {"first": {"type": "number"}, "after": {"type": "string"}}, "required": []}}, {"name": "update-issue", "description": "Updates an existing issue in Linear.", "parameters": {"type": "object", "properties": {"issueId": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "stateId": {"type": "string"}, "assigneeId": {"type": "string"}, "priority": {"type": "number"}, "projectId": {"type": "string"}, "labelIds": {"type": "array", "items": {"type": "string"}}}, "required": ["issueId"]}}, {"name": "update-project", "description": "Updates an existing project in Linear.", "parameters": {"type": "object", "properties": {"projectId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "icon": {"type": "string"}, "color": {"type": "string"}, "state": {"type": "string"}, "teamIds": {"type": "array", "items": {"type": "string"}}}, "required": ["projectId"]}}, {"name": "meetings", "description": "Fetches a list of meetings from Zoom", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "recording-files", "description": "Fetches a list of recordings from Zoom", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "users", "description": "Fetches a list of users from Zoom", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "create-meeting", "description": "Creates a meeting in Zoom.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "create-user", "description": "Creates a user in Zoom. Requires Pro account or higher", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete-meeting", "description": "Deletes a meeting in Zoom", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "delete-user", "description": "Deletes a user in Zoom. Requires Pro account or higher", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "whoami", "description": "Fetch current user information", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "create-sheet", "description": "Creates a new Google Sheet with optional initial data.", "parameters": {"type": "object", "properties": {"title": {"type": "string"}, "sheets": {"type": "array", "items": {"$ref": "#/definitions/GoogleSheetTab"}}}, "required": ["title"]}}, {"name": "fetch-spreadsheet", "description": "Fetches the content of a spreadsheet given its ID.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "update-sheet", "description": "Updates an existing Google Sheet with new data.", "parameters": {"type": "object", "properties": {"spreadsheetId": {"type": "string"}, "updates": {"type": "array", "items": {"$ref": "#/definitions/SheetUpdate"}}}, "required": ["spreadsheetId", "updates"]}}, {"name": "documents", "description": "Sync the metadata of a specified file or folders from Google Drive,\nhandling both individual files and nested folders.\nMetadata required to filter on a particular folder, or file(s). Metadata\nfields should be `{\"files\": [\"<some-id>\"]}` OR\n`{\"folders\": [\"<some-id>\"]}`. The ID should be able to be provided\nby using the Google Picker API\n(https://developers.google.com/drive/picker/guides/overview)\nand using the ID field provided by the response\n(https://developers.google.com/drive/picker/reference/results)", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "documents-fork", "description": "Fetches documents metadata from Google Drive.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "folders", "description": "Sync the folders at the root level of a google drive.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "fetch-document", "description": "Fetches the content of a file given its ID, processes the data using\na response stream, and encodes it into a base64 string. This base64-encoded\nstring can be used to recreate the file in its original format using an external tool.\nIf this is a native google file type then use the fetch-google-sheet or fetch-google-doc\nactions.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "fetch-google-doc", "description": "Fetches the content of a native google document given its ID. Outputs\na JSON reprensentation of a google doc.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "fetch-google-sheet", "description": "Fetches the content of a native google spreadsheet given its ID. Outputs\na JSON representation of a google sheet.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "folder-content", "description": "Fetches the top-level content (files and folders) of a folder given its ID.\nIf no folder ID is provided, it fetches content from the root folder.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "list-documents", "description": "Lists documents in Google Drive with optional filtering by folder ID and document type.", "parameters": {"type": "object", "properties": {"folderId": {"type": "string"}, "mimeType": {"type": "string"}, "pageSize": {"type": "number"}, "pageToken": {"type": "string"}, "orderBy": {"type": "string"}}, "required": []}}, {"name": "list-root-folders", "description": "Lists folders at the root level of Google Drive.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "upload-document", "description": "Uploads a file to Google Drive. The file is uploaded to the root directory\nof the authenticated user's Google Drive account. If a folder ID is provided,\nthe file is uploaded to the specified folder.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "messages", "description": "This sync captures all LinkedIn messages for a Linkedin member for archiving purposes", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get-user-profile", "description": "Gets the authenticated user's profile information from LinkedIn.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "post", "description": "Create a linkedin post with an optional video", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send-post", "description": "Creates a new post on LinkedIn.", "parameters": {"type": "object", "properties": {"text": {"type": "string"}, "visibility": {"type": "string"}}, "required": ["text", "visibility"]}}, {"name": "user-mentions", "description": "Fetches tweets that mention the authenticated user. Initially fetches the last 10 mentions,\nthen uses since_id to track new mentions incrementally.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "user-tweets", "description": "Fetches tweets from a user's timeline. Initially fetches the last 10 tweets,\nthen uses since_id and until_id to track new tweets incrementally.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "get-user-profile", "description": "Gets the authenticated user's profile information from X.", "parameters": {"type": "object", "properties": {}, "required": []}}, {"name": "send-post", "description": "Sends a new post to <PERSON>.", "parameters": {"type": "object", "properties": {"text": {"type": "string"}, "reply_to": {"type": "string"}, "quote": {"type": "string"}}, "required": ["text"]}}]}