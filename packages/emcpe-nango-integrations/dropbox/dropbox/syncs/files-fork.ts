import type { NangoSync, ProxyConfiguration, DropboxFile } from '../../models';
import type { DropboxFileList, DropboxSyncScopeMetadata } from '../types';
import { mapDropboxApiToFile } from '../utils/mappers';

const batchSize = 100;

export default async function fetchData(nango: NangoSync): Promise<void> {
  const metadata = (await nango.getMetadata<DropboxSyncScopeMetadata>()) || {};
  const folders = metadata.folders && metadata.folders.length > 0 ? [...metadata.folders] : [''];
  const files = metadata.files ? [...metadata.files] : [];
  const depthLimit = metadata.folders || metadata.files ? Infinity : 1;

  const batch: DropboxFile[] = [];
  for (const folder of folders) {
    await fetchFolder(nango, folder, depthLimit, batch, 0);
  }

  for (const filePath of files) {
    const fileData = await fetchFile(nango, filePath);
    batch.push(fileData);
    if (batch.length >= batchSize) {
      await nango.batchSave(batch, 'DropboxFile');
      batch.length = 0;
    }
  }

  if (batch.length) {
    await nango.batchSave(batch, 'DropboxFile');
  }
}

async function fetchFolder(
  nango: NangoSync,
  path: string,
  depthLimit: number,
  batch: DropboxFile[],
  depth: number
): Promise<void> {
  const config: ProxyConfiguration = {
    endpoint: `/2/files/list_folder`,
    retries: 10,
    data: {
      path,
      limit: 100,
      recursive: false,
      include_mounted_folders: true,
      include_non_downloadable_files: false,
    },
  };

  let hasMore = true;
  let cursor: string | undefined;

  do {
    const response = await nango.post<DropboxFileList>(
      cursor
        ? {
            endpoint: `/2/files/list_folder/continue`,
            retries: 10,
            data: { cursor },
          }
        : config
    );

    const { entries, has_more, cursor: newCursor } = response.data;
    cursor = newCursor;
    hasMore = has_more;

    for (const entry of entries) {
      if (entry['.tag'] === 'file') {
        const mappedFile = mapDropboxApiToFile(entry);
        batch.push(mappedFile);
        if (batch.length >= batchSize) {
          await nango.batchSave(batch, 'DropboxFile');
          batch.length = 0;
        }
      } else if (entry['.tag'] === 'folder' && depth + 1 <= depthLimit) {
        await fetchFolder(nango, entry.path_lower, depthLimit, batch, depth + 1);
      }
    }
  } while (hasMore);
}

async function fetchFile(nango: NangoSync, path: string): Promise<DropboxFile> {
  const config: ProxyConfiguration = {
    endpoint: '/2/files/get_metadata',
    retries: 10,
    data: { path },
  };

  const response = await nango.post<any>(config);
  return mapDropboxApiToFile(response.data);
}
