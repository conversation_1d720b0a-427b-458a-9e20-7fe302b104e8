import type { NangoAction, HarvestClient, HarvestClientInput } from '../../models';
import { getHarvestAccountId } from '../utils/harvestHelpers';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: HarvestClientInput
): Promise<HarvestClient | NangoError> {
  try {
    if (!input.client_id) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Missing required parameter: client_id is required',
        },
      };
    }

    const accountId = await getHarvestAccountId(nango);
    if (typeof accountId !== 'string') {
      return accountId as NangoError;
    }
    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/v2/clients/${input.client_id}`,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    return response.data;
  } catch (error: any) {
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while fetching the client.';
    console.error('Error fetching client from Harvest:', error);
    return { error: { status, message } };
  }
}
