export interface ActionDefinition {
  provider: string;
  action: string;
  model: string | null;
  description?: string;
}

export interface SyncDefinition {
  provider: string;
  sync: string;
  model: string | null;
  description?: string;
}

export interface ProviderGroup {
  name: string;
  actions: ActionDefinition[];
}

export interface ProviderSummary {
  name: string;
  actions: ActionDefinition[];
  syncs: SyncDefinition[];
}

export interface ActionExecution {
  id: string;
  provider: string;
  action: string;
  input: any;
  output: any;
  valid: boolean;
  timestamp: string;
  status: 'success' | 'error' | 'running';
  error?: string;
}

export interface ActionParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description?: string;
  default?: any;
}

export interface ActionSchema {
  provider: string;
  action: string;
  parameters: ActionParameter[];
  description?: string;
}
