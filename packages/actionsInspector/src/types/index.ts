export interface ActionDefinition {
  provider: string;
  action: string;
  model: string | null;
  description?: string;
}

export interface SyncDefinition {
  provider: string;
  sync: string;
  model: string | null;
  description?: string;
}

export interface ProviderGroup {
  name: string;
  actions: ActionDefinition[];
}

export interface ProviderSummary {
  name: string;
  actions: ActionDefinition[];
  syncs: SyncDefinition[];
}

export interface ActionExecution {
  id: string;
  provider: string;
  action: string;
  input: any;
  output: any;
  valid: boolean;
  timestamp: string;
  status: 'success' | 'error' | 'running';
  error?: string;
}

export interface ActionParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description?: string;
  default?: any;
}

export interface ActionSchema {
  provider: string;
  action: string;
  parameters: ActionParameter[];
  description?: string;
}

export interface ConnectionInfo {
  id: string;
  providerKey: string;
  userId?: string;
  displayName?: string;
  metadata?: Record<string, any>;
  nangoConnection?: {
    id: number;
    provider_config_key: string;
    connection_id: string;
    connection_config: Record<string, string>;
    created_at: string;
    updated_at: string;
    last_fetched_at: string;
    metadata: Record<string, unknown> | null;
    provider: string;
    errors: Array<{
      type: string;
      log_id: string;
    }>;
    end_user: any | null;
    credentials: any;
  };
  syncStatus?: {
    syncs: Array<{
      name: string;
      status: string;
      frequency?: string;
      nextScheduledSyncAt?: string;
    }>;
  };
}
