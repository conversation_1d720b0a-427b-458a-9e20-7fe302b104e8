import { useState, useEffect } from 'react';
import { Play, Code, FileText, Loader2, Database } from 'lucide-react';
import { ActionDefinition } from '@/types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { getActionInputDefinition, loadSampleData } from '@/lib/actionData';
import { ACTION_INPUT_MODELS_JSON_SCHEMA } from '../../../emcpe-server/src/constants';
import { RichParameterDisplay } from '../../../ma-next/src/components/rich-ui/RichParameterDisplay';
import { RichResultDisplay } from '../../../ma-next/src/components/rich-ui/RichResultDisplay';

interface ActionPanelProps {
  action: ActionDefinition | null;
  onExecute: (provider: string, action: string, params: Record<string, any>) => Promise<void>;
  isExecuting: boolean;
  lastResult: any;
}

export function ActionPanel({ action, onExecute, isExecuting, lastResult }: ActionPanelProps) {
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [sampleData, setSampleData] = useState<{ input?: any; output?: any } | null>(null);
  const [loadingSample, setLoadingSample] = useState(false);

  // Load sample data when action changes
  useEffect(() => {
    if (action) {
      setLoadingSample(true);
      loadSampleData(action.provider, action.action)
        .then((data) => {
          setSampleData(data);
          // Pre-populate parameters with sample input data if available
          if (data?.input) {
            setParameters(data.input);
          }
        })
        .finally(() => setLoadingSample(false));
    } else {
      setSampleData(null);
      setParameters({});
    }
  }, [action]);

  if (!action) {
    return (
      <Card className="h-full">
        <CardContent className="flex flex-col items-center justify-center py-12 text-center h-full">
          <Code className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Select an Action</h3>
          <p className="text-muted-foreground">
            Choose an action from the provider list to view its details and execute it.
          </p>
        </CardContent>
      </Card>
    );
  }

  const handleParameterChange = (key: string, value: any) => {
    setParameters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleExecute = async () => {
    await onExecute(action.provider, action.action, parameters);
  };

  const getActionParameters = () => {
    if (!action) return [];

    const inputDef = getActionInputDefinition(action.provider, action.action);
    if (!inputDef?.model) return [];

    const schema = ACTION_INPUT_MODELS_JSON_SCHEMA[inputDef.model];
    if (!schema?.properties) return [];

    return Object.entries(schema.properties).map(([name, prop]: [string, any]) => ({
      name,
      type: prop.type || 'string',
      required: schema.required?.includes(name) || false,
      description: prop.description || ''
    }));
  };

  const renderParameterInput = (param: { name: string; type: string; required: boolean; description: string }) => {
    const value = parameters[param.name] || '';

    if (param.name.includes('body') || param.name.includes('content') || param.name.includes('description') || param.type === 'object') {
      return (
        <Textarea
          placeholder={`Enter ${param.name}...`}
          value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              handleParameterChange(param.name, parsed);
            } catch {
              handleParameterChange(param.name, e.target.value);
            }
          }}
          className="min-h-[100px] font-mono text-xs"
        />
      );
    }

    if (param.type === 'number') {
      return (
        <Input
          type="number"
          placeholder={`Enter ${param.name}...`}
          value={value}
          onChange={(e) => handleParameterChange(param.name, parseFloat(e.target.value) || 0)}
        />
      );
    }

    return (
      <Input
        placeholder={`Enter ${param.name}...`}
        value={value}
        onChange={(e) => handleParameterChange(param.name, e.target.value)}
      />
    );
  };

  const actionParams = getActionParameters();

  return (
    <div className="space-y-6">
      {/* Action Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>{action.provider}:{action.action}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {action.description && (
            <p className="text-sm text-muted-foreground">{action.description}</p>
          )}

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Provider:</span>
              <span className="ml-2 text-muted-foreground">{action.provider}</span>
            </div>
            <div>
              <span className="font-medium">Output Model:</span>
              <span className="ml-2 text-muted-foreground">{action.model || 'None'}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Parameters */}
      <Card>
        <CardHeader>
          <CardTitle>Parameters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {loadingSample && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading sample data...</span>
            </div>
          )}

          {actionParams.length > 0 ? (
            actionParams.map((param) => (
              <div key={param.name} className="space-y-2">
                <label className="text-sm font-medium">
                  {param.name}
                  {param.required && <span className="text-red-500 ml-1">*</span>}
                  {param.description && (
                    <span className="text-xs text-muted-foreground ml-2">
                      ({param.description})
                    </span>
                  )}
                </label>
                {renderParameterInput(param)}
              </div>
            ))
          ) : (
            <div className="text-sm text-muted-foreground">
              No parameters required for this action.
            </div>
          )}

          {/* Rich Parameter Display */}
          {action && Object.keys(parameters).length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Rich Parameter Preview</h4>
              <div className="border rounded-lg p-1">
                <RichParameterDisplay
                  providerKey={action.provider}
                  actionKey={action.action}
                  actionParameters={parameters}
                />
              </div>
            </div>
          )}

          <Separator />

          <div className="space-y-2">
            <label className="text-sm font-medium">Custom JSON Parameters</label>
            <Textarea
              placeholder='{"key": "value"}'
              value={JSON.stringify(parameters, null, 2)}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value);
                  setParameters(parsed);
                } catch {
                  // Invalid JSON, ignore
                }
              }}
              className="min-h-[100px] font-mono text-xs"
            />
          </div>

          <Button
            onClick={handleExecute}
            disabled={isExecuting}
            className="w-full"
          >
            {isExecuting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Executing...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Execute Action
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Sample Output */}
      {sampleData?.output && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-4 w-4" />
              <span>Sample Output</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Rich Result Display */}
            {action && (
              <div>
                <h4 className="text-sm font-medium mb-2">Rich Result Preview</h4>
                <div className="border rounded-lg p-1">
                  <RichResultDisplay
                    result={sampleData.output}
                    providerKey={action.provider}
                    actionKey={action.action}
                    actionParameters={sampleData.input}
                  />
                </div>
              </div>
            )}

            <Separator />

            {/* Raw JSON */}
            <div>
              <h4 className="text-sm font-medium mb-2">Raw JSON</h4>
              <pre className="bg-muted p-4 rounded-md text-xs overflow-auto max-h-96">
                {JSON.stringify(sampleData.output, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle>Last Result</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Rich Result Display */}
            {action && lastResult.success && lastResult.result && (
              <div>
                <h4 className="text-sm font-medium mb-2">Rich Result Preview</h4>
                <div className="border rounded-lg p-1">
                  <RichResultDisplay
                    result={lastResult.result}
                    providerKey={action.provider}
                    actionKey={action.action}
                    actionParameters={parameters}
                  />
                </div>
              </div>
            )}

            <Separator />

            {/* Raw JSON */}
            <div>
              <h4 className="text-sm font-medium mb-2">Raw JSON</h4>
              <pre className="bg-muted p-4 rounded-md text-xs overflow-auto max-h-96">
                {JSON.stringify(lastResult, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
