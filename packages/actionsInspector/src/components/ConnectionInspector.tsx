import { useState, useEffect } from 'react';
import { ChevronDown, Database, User, Clock, AlertCircle, CheckCircle, Pause, Play } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ConnectionInfo } from '@/types';

interface ConnectionInspectorProps {
  providerKey: string;
  selectedConnectionId?: string;
  onConnectionSelect: (connectionId: string) => void;
}

export function ConnectionInspector({ 
  providerKey, 
  selectedConnectionId, 
  onConnectionSelect 
}: ConnectionInspectorProps) {
  const [connections, setConnections] = useState<ConnectionInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const selectedConnection = connections.find(conn => conn.id === selectedConnectionId);

  useEffect(() => {
    if (providerKey) {
      loadConnections();
    }
  }, [providerKey]);

  const loadConnections = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/connections?providerKey=${encodeURIComponent(providerKey)}`);
      const data = await response.json();
      
      if (data.success) {
        setConnections(data.connections || []);
        // Auto-select first connection if none selected
        if (data.connections?.length > 0 && !selectedConnectionId) {
          onConnectionSelect(data.connections[0].id);
        }
      } else {
        setError(data.error || 'Failed to load connections');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load connections');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getSyncStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return <Play className="h-4 w-4 text-green-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSyncStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'success':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Connection Inspector</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">Loading connections...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Connection Inspector</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-red-600">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>{error}</p>
            <Button onClick={loadConnections} className="mt-2" variant="outline" size="sm">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Connection Inspector</span>
          </div>
          <Badge variant="secondary">{connections.length} connection{connections.length !== 1 ? 's' : ''}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {connections.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            <Database className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No connections found for {providerKey}</p>
          </div>
        ) : (
          <>
            {/* Connection Selector */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Select Connection:</label>
              <Select value={selectedConnectionId || ''} onValueChange={onConnectionSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a connection..." />
                </SelectTrigger>
                <SelectContent>
                  {connections.map((connection) => (
                    <SelectItem key={connection.id} value={connection.id}>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <span>{connection.displayName || connection.id}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Selected Connection Details */}
            {selectedConnection && (
              <div className="space-y-4">
                <Separator />
                
                {/* Basic Connection Info */}
                <div className="space-y-2">
                  <h4 className="font-medium">Connection Details</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">ID:</span>
                      <div className="font-mono text-xs break-all">{selectedConnection.id}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Provider:</span>
                      <div>{selectedConnection.providerKey}</div>
                    </div>
                    {selectedConnection.displayName && (
                      <div>
                        <span className="text-muted-foreground">Display Name:</span>
                        <div>{selectedConnection.displayName}</div>
                      </div>
                    )}
                    {selectedConnection.nangoConnection && (
                      <>
                        <div>
                          <span className="text-muted-foreground">Created:</span>
                          <div>{formatDate(selectedConnection.nangoConnection.created_at)}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Last Fetched:</span>
                          <div>{formatDate(selectedConnection.nangoConnection.last_fetched_at)}</div>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Sync Status */}
                {selectedConnection.syncStatus?.syncs && selectedConnection.syncStatus.syncs.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Sync Status</h4>
                    <div className="space-y-2">
                      {selectedConnection.syncStatus.syncs.map((sync, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center space-x-2">
                            {getSyncStatusIcon(sync.status)}
                            <span className="font-medium">{sync.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className={getSyncStatusColor(sync.status)}>
                              {sync.status}
                            </Badge>
                            {sync.frequency && (
                              <span className="text-xs text-muted-foreground">{sync.frequency}</span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Metadata */}
                {selectedConnection.metadata && Object.keys(selectedConnection.metadata).length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Metadata</h4>
                    <div className="bg-muted p-3 rounded text-xs font-mono max-h-40 overflow-auto">
                      <pre>{JSON.stringify(selectedConnection.metadata, null, 2)}</pre>
                    </div>
                  </div>
                )}

                {/* Errors */}
                {selectedConnection.nangoConnection?.errors && selectedConnection.nangoConnection.errors.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-red-600">Errors</h4>
                    <div className="space-y-1">
                      {selectedConnection.nangoConnection.errors.map((error, index) => (
                        <div key={index} className="flex items-center space-x-2 text-sm text-red-600">
                          <AlertCircle className="h-4 w-4" />
                          <span>{error.type}</span>
                          <span className="text-xs text-muted-foreground">({error.log_id})</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
