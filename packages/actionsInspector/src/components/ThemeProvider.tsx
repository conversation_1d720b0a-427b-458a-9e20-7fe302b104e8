import { createContext, useContext, type ReactNode } from 'react'
import { useTheme } from '@/lib/useTheme'

const ThemeContext = createContext(returnDummy())

function returnDummy() {
  return { theme: 'light' as 'light' | 'dark', toggleTheme: () => {} }
}

export function ThemeProvider({ children }: { children: ReactNode }) {
  const value = useTheme()
  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
}

export function useThemeContext() {
  return useContext(ThemeContext)
}
