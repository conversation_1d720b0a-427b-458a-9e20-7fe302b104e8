import { useState } from 'react';
import { ChevronRight, Play, Settings } from 'lucide-react';
import { ProviderSummary } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { cn } from '@/lib/utils';

interface ProviderListProps {
  providers: ProviderSummary[];
  selected: { provider: string; key: string; type: 'action' | 'sync' | 'provider-script' } | null;
  onActionSelect: (provider: string, action: string) => void;
  onSyncSelect: (provider: string, sync: string) => void;
  onRunAction: (provider: string, action: string) => void;
  onRunSync: (provider: string, sync: string) => void;
  onRunProviderScript: (provider: string) => void;
}

export function ProviderList({
  providers,
  selected,
  onActionSelect,
  onSyncSelect,
  onRunAction,
  onRunSync,
  onRunProviderScript,
}: ProviderListProps) {
  const [expandedProviders, setExpandedProviders] = useState<string[]>([]);

  const toggleProvider = (provider: string) => {
    setExpandedProviders(prev => {
      const isExpanding = !prev.includes(provider);
      const newExpanded = prev.includes(provider)
        ? prev.filter(p => p !== provider)
        : [...prev, provider];

      // Auto-select first item when expanding a provider
      if (isExpanding) {
        const providerData = providers.find(p => p.name === provider);
        if (providerData) {
          // First try provider script if available
          if (hasProviderScript(provider)) {
            onRunProviderScript(provider);
          }
          // Then try first sync
          else if (providerData.syncs.length > 0) {
            onSyncSelect(provider, providerData.syncs[0].sync);
          }
          // Finally try first action
          else if (providerData.actions.length > 0) {
            onActionSelect(provider, providerData.actions[0].action);
          }
        }
      }

      return newExpanded;
    });
  };

  const isActionSelected = (provider: string, action: string) => {
    return (
      selected?.type === 'action' && selected?.provider === provider && selected.key === action
    );
  };

  const isProviderScriptSelected = (provider: string) => {
    return selected?.type === 'provider-script' && selected?.provider === provider;
  };

  const isSyncSelected = (provider: string, sync: string) => {
    return selected?.type === 'sync' && selected.provider === provider && selected.key === sync;
  };

  const hasProviderScript = (provider: string) => {
    // Only show provider script for providers that have test scripts
    return [
      'slack',
      'google-drive', 
      'dropbox',
      'github',
      'google-calendar',
      'google-docs',
      'google-mail',
      'google-sheet',
      'harvest',
      'linear',
      'linkedin',
      'notion',
      'x-social',
      'twitter-v2'
    ].includes(provider);
  };

  const getProviderIcon = (providerName: string) => {
    // You can customize icons based on provider
    switch (providerName) {
      case 'slack':
        return '💬';
      case 'github':
        return '🐙';
      case 'google-calendar':
      case 'google-mail':
      case 'google-docs':
      case 'google-drive':
      case 'google-sheet':
        return '🧭';
      case 'notion':
        return '📝';
      case 'linear':
        return '📋';
      case 'harvest':
        return '⏰';
      case 'linkedin':
        return '💼';
      case 'twitter-v2':
        return '🐦';
      default:
        return '⚙️';
    }
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>Providers</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Accordion
          type="multiple"
          value={expandedProviders}
          onValueChange={setExpandedProviders}
          className="w-full"
        >
          {providers.map(provider => (
            <AccordionItem key={provider.name} value={provider.name} className="border-b-0">
              <AccordionTrigger className="px-6 py-3 hover:bg-muted/50">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{getProviderIcon(provider.name)}</span>
                    <div className="text-left">
                      <div className="font-medium">{provider.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {provider.actions.length} action{provider.actions.length !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>
                  {hasProviderScript(provider.name) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-2 h-8 w-8 p-0"
                      onClick={e => {
                        e.stopPropagation();
                        onRunProviderScript(provider.name);
                      }}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="pb-0">
                <div className="space-y-1">
                  {/* Provider Script Option */}
                  {hasProviderScript(provider.name) && (
                    <div
                      className={cn(
                        'flex items-center justify-between px-6 py-2 hover:bg-muted/50 cursor-pointer border-l-2 border-transparent',
                        isProviderScriptSelected(provider.name) && 'bg-muted border-l-primary'
                      )}
                      onClick={() => onRunProviderScript(provider.name)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-blue-600 dark:text-blue-400 ml-5">
                          🚀 Provider Script
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-2 h-8 w-8 p-0"
                        onClick={e => {
                          e.stopPropagation();
                          onRunProviderScript(provider.name);
                        }}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    </div>
                  )}

                  {/* Syncs */}
                  {provider.syncs.length > 0 && (
                    <div className="mt-2 ml-5 text-xs font-semibold text-muted-foreground">
                      Syncs
                    </div>
                  )}
                  {provider.syncs.map(sync => (
                    <div
                      key={`sync-${sync.sync}`}
                      className={cn(
                        'flex items-center justify-between px-6 py-2 hover:bg-muted/50 cursor-pointer border-l-2 border-transparent ml-5',
                        isSyncSelected(provider.name, sync.sync) && 'bg-muted border-l-primary'
                      )}
                      onClick={() => onSyncSelect(provider.name, sync.sync)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">{sync.sync}</div>
                        {sync.description && (
                          <div className="text-xs text-muted-foreground truncate">
                            {sync.description}
                          </div>
                        )}
                        {sync.model && (
                          <div className="text-xs text-blue-600 dark:text-blue-400 truncate">
                            → {sync.model}
                          </div>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-2 h-8 w-8 p-0"
                        onClick={e => {
                          e.stopPropagation();
                          onRunSync(provider.name, sync.sync);
                        }}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}

                  {provider.actions.length > 0 && (
                    <div className="mt-2 ml-5 text-xs font-semibold text-muted-foreground">
                      Actions
                    </div>
                  )}
                  {/* Individual Actions */}
                  {provider.actions.map(action => (
                    <div
                      key={action.action}
                      className={cn(
                        'flex items-center justify-between px-6 py-2 hover:bg-muted/50 cursor-pointer border-l-2 border-transparent ml-5',
                        isActionSelected(provider.name, action.action) &&
                          'bg-muted border-l-primary'
                      )}
                      onClick={() => onActionSelect(provider.name, action.action)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">{action.action}</div>
                        {action.description && (
                          <div className="text-xs text-muted-foreground truncate">
                            {action.description}
                          </div>
                        )}
                        {action.model && (
                          <div className="text-xs text-blue-600 dark:text-blue-400 truncate">
                            → {action.model}
                          </div>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-2 h-8 w-8 p-0"
                        onClick={e => {
                          e.stopPropagation();
                          onRunAction(provider.name, action.action);
                        }}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
}
