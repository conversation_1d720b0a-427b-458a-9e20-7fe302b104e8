import type { NextApiRequest, NextApiResponse } from 'next';
import { <PERSON>go } from '@nangohq/node';
import { supabase } from '../../lib/supabase';

async function getConnectionId(providerKey: string): Promise<string | null> {
  const { data } = await supabase
    .from('connections')
    .select('id')
    .eq('providerKey', providerKey)
    .limit(1)
    .maybeSingle();
  return data?.id || null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  const { provider, model, modifiedAfter, connectionId: requestedConnectionId } = req.body as {
    provider: string;
    model: string;
    modifiedAfter?: string;
    connectionId?: string;
  };

  if (!provider || !model) {
    return res.status(400).json({ success: false, error: 'provider and model are required' });
  }

  // Use the requested connection ID or find one for the provider
  let connectionId = requestedConnectionId;
  if (!connectionId) {
    connectionId = await getConnectionId(provider);
    if (!connectionId) {
      return res.status(400).json({ success: false, error: `No ${provider} connection found` });
    }
  }

  if (!process.env.NANGO_SECRET_KEY) {
    return res.status(500).json({ success: false, error: 'NANGO_SECRET_KEY not set' });
  }

  const nango = new Nango({ secretKey: process.env.NANGO_SECRET_KEY });

  try {
    const result = await nango.listRecords({
      providerConfigKey: provider,
      connectionId,
      model,
      ...(modifiedAfter ? { modifiedAfter } : {}),
    });
    res.status(200).json({ success: true, result });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message || 'Internal error' });
  }
}
