import type { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs/promises';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { params } = req.query;
  
  if (!Array.isArray(params) || params.length !== 2) {
    return res.status(400).json({ error: 'Invalid parameters' });
  }

  const [provider, action] = params;
  
  try {
    const filePath = path.join(process.cwd(), 'result-data', provider, `${action}.json`);
    const fileContent = await fs.readFile(filePath, 'utf-8');
    const data = JSON.parse(fileContent);
    
    res.status(200).json(data);
  } catch (error) {
    // File doesn't exist or can't be read
    res.status(404).json({ error: 'Sample data not found' });
  }
}
