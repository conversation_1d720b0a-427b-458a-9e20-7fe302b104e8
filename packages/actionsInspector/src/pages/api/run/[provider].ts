import type { NextApiRequest, NextApiResponse } from 'next';
import { runSlackTest } from '../../../tests/slack';
import { runGoogleDriveTest } from '../../../tests/google-drive';
import { runDropboxTest } from '../../../tests/dropbox';
import { runGithubTest } from '../../../tests/github';
import { runGoogleCalendarTest } from '../../../tests/google-calendar';
import { runGoogleDocsTest } from '../../../tests/google-docs';
import { runGoogleMailTest } from '../../../tests/google-mail';
import { runGoogleSheetTest } from '../../../tests/google-sheet';
import { runHarvestTest } from '../../../tests/harvest';
import { runLinearTest } from '../../../tests/linear';
import { runLinkedinTest } from '../../../tests/linkedin';
import { runNotionTest } from '../../../tests/notion';
import { runXSocialTest } from '../../../tests/x-social';

const runners: Record<string, () => Promise<any[]>> = {
  slack: runSlackTest,
  'google-drive': runGoogleDriveTest,
  dropbox: runDropboxTest,
  github: runGithubTest,
  'google-calendar': runGoogleCalendarTest,
  'google-docs': runGoogleDocsTest,
  'google-mail': runGoogleMailTest,
  'google-sheet': runGoogleSheetTest,
  harvest: runHarvestTest,
  linear: runLinearTest,
  linkedin: runLinkedinTest,
  notion: runNotionTest,
  'x-social': runXSocialTest,
  'twitter-v2': runXSocialTest,
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') return res.status(405).end();
  const { provider } = req.query;
  const runner = typeof provider === 'string' ? runners[provider] : undefined;
  if (!runner) return res.status(404).json({ error: 'unknown provider' });
  try {
    const results = await runner();
    res.status(200).json({ results });
  } catch (err: any) {
    res.status(500).json({ error: err.message });
  }
}
