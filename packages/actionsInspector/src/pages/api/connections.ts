import type { NextApiRequest, NextApiResponse } from 'next';
import { Nango } from '@nangohq/node';
import { supabase } from '../../lib/supabase';
import { ConnectionInfo } from '../../types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  const { providerKey, connectionId } = req.query as {
    providerKey?: string;
    connectionId?: string;
  };

  if (!providerKey) {
    return res.status(400).json({ success: false, error: 'providerKey is required' });
  }

  if (!process.env.NANGO_SECRET_KEY) {
    return res.status(500).json({ success: false, error: 'NANGO_SECRET_KEY not set' });
  }

  const nango = new Nango({ secretKey: process.env.NANGO_SECRET_KEY });

  try {
    // Get connections from database
    let query = supabase
      .from('connections')
      .select('id, providerKey, userId, displayName, metadata')
      .eq('providerKey', providerKey);

    if (connectionId) {
      query = query.eq('id', connectionId);
    }

    const { data: dbConnections, error: dbError } = await query;

    if (dbError) {
      throw new Error(`Database error: ${dbError.message}`);
    }

    if (!dbConnections || dbConnections.length === 0) {
      return res.status(200).json({ 
        success: true, 
        connections: [],
        message: `No connections found for provider: ${providerKey}`
      });
    }

    // Enrich with Nango data
    const enrichedConnections: ConnectionInfo[] = [];

    for (const dbConnection of dbConnections) {
      const connectionInfo: ConnectionInfo = {
        id: dbConnection.id,
        providerKey: dbConnection.providerKey,
        userId: dbConnection.userId,
        displayName: dbConnection.displayName,
        metadata: dbConnection.metadata,
      };

      try {
        // Get Nango connection details
        const nangoConnection = await nango.getConnection(providerKey, dbConnection.id);
        connectionInfo.nangoConnection = nangoConnection;

        // Get sync status for this connection
        const syncStatus = await nango.syncStatus(providerKey, '*', dbConnection.id);
        connectionInfo.syncStatus = syncStatus;

        // Get metadata from Nango
        try {
          const nangoMetadata = await nango.getMetadata(providerKey, dbConnection.id);
          if (nangoMetadata) {
            connectionInfo.metadata = {
              ...connectionInfo.metadata,
              nango: nangoMetadata,
            };
          }
        } catch (metadataError) {
          console.warn(`Could not fetch metadata for connection ${dbConnection.id}:`, metadataError);
        }
      } catch (nangoError: any) {
        console.warn(`Could not fetch Nango data for connection ${dbConnection.id}:`, nangoError.message);
        // Still include the connection even if Nango data is unavailable
      }

      enrichedConnections.push(connectionInfo);
    }

    res.status(200).json({ 
      success: true, 
      connections: enrichedConnections 
    });
  } catch (error: any) {
    console.error('Error fetching connections:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message || 'Internal error' 
    });
  }
}
