import { executeProviderActions } from './providerRunner';

export async function runLinkedinTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('linkedin', [
    { actionKey: 'get-user-profile', params: {} },
  ], onProgress);

  const more = await executeProviderActions('linkedin', [
    { 
      actionKey: 'send-post', 
      params: { 
        content: 'Testing LinkedIn integration with ActionsInspector! 🚀 #automation #testing'
      } 
    },
  ], onProgress);
  results.push(...more);

  return results;
}
