import { executeProviderActions } from './providerRunner';

export async function runXSocialTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('twitter-v2', [
    { actionKey: 'get-user-profile', params: {} },
  ], onProgress);

  const more = await executeProviderActions('twitter-v2', [
    { 
      actionKey: 'send-post', 
      params: { 
        text: 'Testing X (Twitter) integration with ActionsInspector! 🚀 #automation #testing'
      } 
    },
  ], onProgress);
  results.push(...more);

  return results;
}
