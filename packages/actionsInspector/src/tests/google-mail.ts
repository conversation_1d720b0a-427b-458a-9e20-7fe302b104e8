import { executeProviderActions } from './providerRunner';

export async function runGoogleMailTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('google-mail', [
    { actionKey: 'list-messages', params: { maxResults: 10 } },
  ], onProgress);

  const messages = (results[0].output as any)?.messages;
  if (messages && messages.length > 0) {
    const firstMessage = messages[0];
    
    const more = await executeProviderActions('google-mail', [
      { actionKey: 'get-message', params: { id: firstMessage.id } },
    ], onProgress);
    results.push(...more);

    const final = await executeProviderActions('google-mail', [
      { 
        actionKey: 'compose-draft', 
        params: { 
          to: '<EMAIL>',
          subject: 'ActionsInspector Test Draft',
          body: 'This is a test draft created by ActionsInspector'
        } 
      },
    ], onProgress);
    results.push(...final);
  }

  return results;
}
