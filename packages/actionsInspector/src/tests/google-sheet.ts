import { executeProviderActions } from './providerRunner';

export async function runGoogleSheetTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('google-sheet', [
    { 
      actionKey: 'create-sheet', 
      params: { 
        title: 'ActionsInspector Test Sheet',
        data: [
          ['Name', 'Email', 'Role'],
          ['<PERSON>', '<EMAIL>', 'Developer'],
          ['<PERSON>', '<EMAIL>', 'Designer']
        ]
      } 
    },
  ], onProgress);

  const spreadsheetId = (results[0].output as any)?.spreadsheetId;
  
  if (spreadsheetId) {
    const more = await executeProviderActions('google-sheet', [
      { actionKey: 'fetch-spreadsheet', params: { spreadsheetId } },
    ], onProgress);
    results.push(...more);

    const final = await executeProviderActions('google-sheet', [
      { 
        actionKey: 'update-sheet', 
        params: { 
          spreadsheetId,
          range: 'A4:C4',
          values: [['<PERSON>', '<EMAIL>', 'Manager']]
        } 
      },
    ], onProgress);
    results.push(...final);
  }

  return results;
}
