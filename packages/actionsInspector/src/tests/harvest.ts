import { executeProviderActions } from './providerRunner';

export async function runHarvestTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('harvest', [
    { actionKey: 'list-clients', params: {} },
    { actionKey: 'list-projects', params: {} },
  ], onProgress);

  const projects = (results[1].output as any)?.projects;
  const activeProject = projects?.find((project: any) => project.is_active);

  if (activeProject) {
    const more = await executeProviderActions('harvest', [
      { actionKey: 'get-project', params: { id: activeProject.id } },
      { actionKey: 'list-project-tasks', params: { projectId: activeProject.id } },
    ], onProgress);
    results.push(...more);

    const tasks = (more[1].output as any)?.task_assignments;
    const activeTask = tasks?.find((task: any) => task.is_active);

    if (activeTask) {
      const final = await executeProviderActions('harvest', [
        { 
          actionKey: 'start-timer', 
          params: { 
            project_id: activeProject.id,
            task_id: activeTask.task.id,
            notes: 'ActionsInspector test timer'
          } 
        },
      ], onProgress);
      results.push(...final);

      const timerId = (final[0].output as any)?.id;
      if (timerId) {
        const stopTimer = await executeProviderActions('harvest', [
          { actionKey: 'stop-timer', params: { id: timerId } },
        ], onProgress);
        results.push(...stopTimer);
      }
    }
  }

  return results;
}
