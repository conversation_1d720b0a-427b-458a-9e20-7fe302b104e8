import { executeProviderActions } from './providerRunner';

export async function runGoogleCalendarTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('google-calendar', [
    { actionKey: 'list-calendars', params: {} },
  ], onProgress);

  const calendars = (results[0].output as any)?.calendars;
  const primaryCalendar = calendars?.find((cal: any) => cal.primary) || calendars?.[0];

  if (primaryCalendar) {
    const more = await executeProviderActions('google-calendar', [
      { actionKey: 'list-events', params: { calendarId: primaryCalendar.id } },
    ], onProgress);
    results.push(...more);

    // Create a test event
    const eventTime = new Date();
    eventTime.setHours(eventTime.getHours() + 1);
    const endTime = new Date(eventTime);
    endTime.setHours(endTime.getHours() + 1);

    const final = await executeProviderActions('google-calendar', [
      { 
        actionKey: 'create-event', 
        params: { 
          calendarId: primaryCalendar.id,
          summary: 'ActionsInspector Test Event',
          description: 'Created by ActionsInspector test',
          start: { dateTime: eventTime.toISOString() },
          end: { dateTime: endTime.toISOString() }
        } 
      },
    ], onProgress);
    results.push(...final);
  }

  return results;
}
