import { executeProviderActions } from './providerRunner';

export async function runGithubTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions('github', [
    { actionKey: 'list-repositories', params: {} },
  ], onProgress);

  const repos = (results[0].output as any)?.repositories;
  const testRepo = repos?.find((repo: any) => repo.name.toLowerCase().includes('test') || repo.name.toLowerCase().includes('demo'));
  
  if (testRepo) {
    const more = await executeProviderActions('github', [
      { actionKey: 'list-issues', params: { owner: testRepo.owner?.login || testRepo.owner?.name, repo: testRepo.name } },
      { actionKey: 'list-pull-requests', params: { owner: testRepo.owner?.login || testRepo.owner?.name, repo: testRepo.name } },
    ], onProgress);
    results.push(...more);

    const final = await executeProviderActions('github', [
      { actionKey: 'list-branches', params: { owner: testRepo.owner?.login || testRepo.owner?.name, repo: testRepo.name } },
    ], onProgress);
    results.push(...final);
  }

  return results;
}
