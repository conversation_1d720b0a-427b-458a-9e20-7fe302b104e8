{"name": "@makeagent/actions-inspector", "version": "1.0.0", "description": "Localhost-only tool for testing Nango actions", "private": true, "scripts": {"dev": "next dev --port 3002 --turbo", "build": "next build", "start": "next start --port 3002", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@supabase/supabase-js": "^2.45.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.300.0", "next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-syntax-highlighter": "^15.5.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-syntax-highlighter": "^15.5.0", "autoprefixer": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.0.0", "tailwindcss": "^3.0.0", "typescript": "^5.0.0"}}