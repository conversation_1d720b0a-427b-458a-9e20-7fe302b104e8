# Actions Inspector

A localhost-only development tool for exercising Nango actions. The initial implementation targeted **Slack** and now also includes a **Google Drive** flow.

Running a provider test executes a sequence of actions using your local Netlify functions. Outputs are validated against the Zod schemas from `nangoIntrospection` and written to `data/<provider>/` for later reference.

## Usage

```bash
cd packages/actionsInspector
npm install
npm run dev
# open http://localhost:3002
```

Use the on page buttons to run the Slack or Google Drive tests.
