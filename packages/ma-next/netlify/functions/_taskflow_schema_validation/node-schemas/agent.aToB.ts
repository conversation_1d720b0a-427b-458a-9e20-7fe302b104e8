import { z } from 'zod';
import {
  ACTION_INPUT_MODELS_KEYED,
  ACTION_OUTPUT_MODELS_KEYED,
} from '../../_agents/nangoConstants';

const outputInfoSchema = z.object({ providerKey: z.string(), actionKey: z.string() });

export const aToBParameters = z.object({
  system: z.string(),
  prompt: z.string(),
  output: outputInfoSchema,
  userDeterminedParameters: z.record(z.any()),
});

function removeProvidedParametersFromSchema(originalSchema: any, userParams: Record<string, any>) {
  if (!originalSchema || typeof originalSchema !== 'object') return originalSchema;
  const schema = JSON.parse(JSON.stringify(originalSchema));
  const providedKeys = Object.keys(userParams);
  if (schema.properties && typeof schema.properties === 'object') {
    providedKeys.forEach(key => {
      if (schema.properties[key]) delete schema.properties[key];
    });
    if (Array.isArray(schema.required)) {
      schema.required = schema.required.filter((key: string) => !providedKeys.includes(key));
    }
  }
  return schema;
}

export function aToBOutput(params: z.infer<typeof aToBParameters>) {
  const key = `${params.output.providerKey}:${params.output.actionKey}` as const;
  const inputInfo = (ACTION_INPUT_MODELS_KEYED as any)[key];
  const outputInfo = (ACTION_OUTPUT_MODELS_KEYED as any)[key];
  if (!inputInfo || !outputInfo) throw new Error(`Unknown provider action: ${key}`);

  // Validate user provided parameters
  inputInfo.zodSchema.partial().parse(params.userDeterminedParameters);

  // Compute schema for AI output (not returned but ensures existence)
  removeProvidedParametersFromSchema(inputInfo.jsonSchema, params.userDeterminedParameters);

  return outputInfo.jsonSchema as any;
}
