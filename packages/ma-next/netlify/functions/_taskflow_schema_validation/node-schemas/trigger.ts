import { z } from 'zod';
import { SYNC_OUTPUTS_KEYED, SYNC_OUTPUT_MODELS_JSON_SCHEMA } from '../../_agents/nangoConstants';

export const triggerParameters = z.object({
  providerKey: z.string(),
  model: z.string(),
  syncKey: z.string(),
  label: z.string(),
  description: z.string(),
});

export function triggerOutput(params: z.infer<typeof triggerParameters>) {
  const modelName = SYNC_OUTPUTS_KEYED[`${params.providerKey}:${params.syncKey}`];
  const schema = SYNC_OUTPUT_MODELS_JSON_SCHEMA[modelName];
  if (!schema) throw new Error(`Unknown sync output model for ${params.providerKey}:${params.syncKey}`);
  return schema as any;
}
