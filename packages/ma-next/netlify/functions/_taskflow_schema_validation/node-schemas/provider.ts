import { z } from 'zod';
import {
  ACTION_INPUT_MODELS_KEYED,
  ACTION_OUTPUT_MODELS_KEYED,
} from '../../_agents/nangoConstants';

export function getProviderSchemas(nodeType: string) {
  const [, provider, action] = nodeType.split('.');
  const key = `${provider}:${action}` as const;
  const input = (ACTION_INPUT_MODELS_KEYED as any)[key];
  const output = (ACTION_OUTPUT_MODELS_KEYED as any)[key];
  if (!input || !output) throw new Error(`Unknown provider action: ${key}`);
  return {
    parameters: input.zodSchema as z.ZodTypeAny,
    output: () => output.jsonSchema as any,
  };
}
