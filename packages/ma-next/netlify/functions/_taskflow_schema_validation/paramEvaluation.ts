import { JsonSchema } from './nodeSchemas';

function schemaHasPath(schema: JsonSchema, path: string[]): boolean {
  let current: JsonSchema | undefined = schema;
  for (const key of path) {
    if (!current || current.type !== 'object' || !current.properties) return false;
    current = current.properties[key];
  }
  return !!current;
}

function extractPaths(expr: string, keys: string[], schema: JsonSchema): string[][] {
  const paths: string[][] = [];
  for (const key of keys) {
    const regex = new RegExp(`${key}(?:\\.[a-zA-Z0-9_]+)*`, 'g');
    for (const match of expr.matchAll(regex)) {
      const segments = match[0].split('.');
      const originalLength = segments.length;
      let current = [...segments];
      while (current.length && !schemaHasPath(schema, current)) current.pop();
      if (current.length === 0 || (current.length <= 1 && current.length < originalLength)) {
        throw new Error(`Invalid reference ${segments.join('.')} in template`);
      }
      paths.push(current);
    }
  }
  return paths;
}

function validateTemplateString(template: string, schema: JsonSchema, keys: string[]) {
  const placeholder = /{{\s*([^}]+)\s*}}/g;
  for (const [, expr] of template.matchAll(placeholder)) {
    const paths = extractPaths(expr, keys, schema);
    for (const path of paths) {
      if (!schemaHasPath(schema, path)) {
        throw new Error(`Invalid reference ${path.join('.')} in template`);
      }
    }
  }
}

export function validateParameterTemplates(params: any, schema: JsonSchema, keys: string[]) {
  if (typeof params === 'string') {
    validateTemplateString(params, schema, keys);
  } else if (Array.isArray(params)) {
    for (const item of params) validateParameterTemplates(item, schema, keys);
  } else if (typeof params === 'object' && params) {
    for (const value of Object.values(params)) validateParameterTemplates(value, schema, keys);
  }
}
