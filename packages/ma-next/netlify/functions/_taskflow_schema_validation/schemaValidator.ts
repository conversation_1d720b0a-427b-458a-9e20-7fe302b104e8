import { getNodeSchemas, JsonSchema } from './nodeSchemas';
import { validateParameterTemplates } from './paramEvaluation';

export function validateTaskflowSchema(definition: any): boolean {
  const contextSchema: JsonSchema = { type: 'object', properties: {} };
  let first = true;
  for (const node of definition.nodes) {
    const schemas = getNodeSchemas(node.type);
    schemas.parameters.parse(node.parameters);
    const keys = Object.keys(contextSchema.properties || {});
    validateParameterTemplates(node.parameters, contextSchema, keys);
    const output = schemas.output(node.parameters);
    const key = first && node.type.startsWith('trigger.') ? 'trigger' : node.id;
    contextSchema.properties![key] = output;
    first = false;
  }
  return true;
}
