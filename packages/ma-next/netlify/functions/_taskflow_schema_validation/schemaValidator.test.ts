import { test } from 'node:test';
import { strictEqual, throws } from 'node:assert';
import { EMAIL_WORKFLOW_SCHEMA, A_TO_B_INSTANCE_SCHEMA } from '../_agents/sampleWorkflows';
import { validateTaskflowSchema } from './schemaValidator';

// Successful validation of sample workflow
test('validate email workflow schema', () => {
  const result = validateTaskflowSchema(EMAIL_WORKFLOW_SCHEMA);
  strictEqual(result, true);
});

// Failure when referencing unknown trigger property
test('fails on bad template reference', () => {
  const bad = JSON.parse(JSON.stringify(EMAIL_WORKFLOW_SCHEMA));
  bad.nodes[1].parameters.prompt = '{{trigger.missing}}';
  throws(() => validateTaskflowSchema(bad));
});

// Successful validation of aToB workflow
test('validate aToB workflow schema', () => {
  const good = JSON.parse(JSON.stringify(A_TO_B_INSTANCE_SCHEMA));
  good.nodes[0].parameters.providerKey = 'google-mail';
  good.nodes[0].parameters.syncKey = 'emails-fork';
  good.nodes[0].parameters.model = 'GmailEmail';
  const result = validateTaskflowSchema(good);
  strictEqual(result, true);
});

// Failure when userDeterminedParameters invalid
test('fails when aToB user parameters invalid', () => {
  const bad = JSON.parse(JSON.stringify(A_TO_B_INSTANCE_SCHEMA));
  bad.nodes[1].parameters.userDeterminedParameters = { owner: 123 };
  throws(() => validateTaskflowSchema(bad));
});
