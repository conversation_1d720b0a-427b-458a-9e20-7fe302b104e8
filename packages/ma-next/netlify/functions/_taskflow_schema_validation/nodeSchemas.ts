import { z } from 'zod';
import { triggerParameters, triggerOutput } from './node-schemas/trigger';
import { aiSimpleParameters, aiSimpleOutput } from './node-schemas/ai.simple';
import { getProviderSchemas } from './node-schemas/provider';
import { aToBParameters, aToBOutput } from './node-schemas/agent.aToB';

export type JsonSchema = {
  type: string;
  properties?: Record<string, JsonSchema>;
  items?: JsonSchema;
  required?: string[];
};

export type NodeSchemas = {
  parameters: z.ZodTypeAny;
  output: (params: any) => JsonSchema;
};

const SCHEMAS: Record<string, NodeSchemas | ((type: string) => NodeSchemas)> = {
  'trigger.syncTrigger': { parameters: triggerParameters, output: triggerOutput },
  'ai.simple': { parameters: aiSimpleParameters, output: aiSimpleOutput },
  'agent.aToB': { parameters: aToBParameters, output: aToBOutput },
  provider: getProviderSchemas,
};

export function getNodeSchemas(nodeType: string): NodeSchemas {
  if (SCHEMAS[nodeType]) {
    const entry = SCHEMAS[nodeType];
    return typeof entry === 'function' ? (entry as any)(nodeType) : entry;
  }
  if (nodeType.startsWith('provider.')) return (SCHEMAS.provider as any)(nodeType);
  throw new Error(`Unsupported node type: ${nodeType}`);
}
