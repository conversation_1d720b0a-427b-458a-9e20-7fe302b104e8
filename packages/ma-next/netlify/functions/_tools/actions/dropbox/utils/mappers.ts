import type { DropboxFile } from '../../models';

/**
 * Maps Dropbox API file metadata response to DropboxFile format
 */
export function mapDropboxApiToFile(apiResponse: any, downloadUrl?: string): DropboxFile {
  const mappedFile: DropboxFile = {
    id: apiResponse.id || apiResponse.path_lower,
    name: apiResponse.name,
    path_display: apiResponse.path_display,
    path_lower: apiResponse.path_lower,
    size: apiResponse.size || 0,
    server_modified: apiResponse.server_modified || apiResponse.client_modified || '',
  };

  // Only add optional properties if they have values
  if (apiResponse.content_hash !== undefined) {
    mappedFile.content_hash = apiResponse.content_hash;
  }

  if (apiResponse.content !== undefined) {
    mappedFile.content = apiResponse.content;
  }

  // Handle download URL with rewriting for direct access
  if (downloadUrl !== undefined) {
    mappedFile.download_url = downloadUrl.replace('www.dropbox.com', 'dl.dropboxusercontent.com');
  } else if (apiResponse.download_url !== undefined) {
    mappedFile.download_url = apiResponse.download_url.replace('www.dropbox.com', 'dl.dropboxusercontent.com');
  }

  return mappedFile;
}
