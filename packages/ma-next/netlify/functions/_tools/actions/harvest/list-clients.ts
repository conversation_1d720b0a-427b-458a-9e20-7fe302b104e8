import type { NangoAction, HarvestClientList } from '../models';
import { getHarvestAccountId } from './utils/harvestHelpers';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  _input?: void
): Promise<HarvestClientList | ActionError> {
  try {
    const accountIdResult = await getHarvestAccountId(nango);

    if (typeof accountIdResult !== 'string' && 'error' in accountIdResult) {
      console.error('Failed to get Harvest Account ID:', accountIdResult.error.message);
      return accountIdResult;
    }
    const accountId = accountIdResult;

    const response = await nango.proxy({
      method: 'GET',
      endpoint: '/v2/clients',
      params: {
        per_page: 100,
        page: 1,
      },
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Error fetching clients from Harvest:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while fetching clients.';

    return {
      error: {
        status: status,
        message: `Failed to fetch clients: ${message}`,
      },
    };
  }
}
