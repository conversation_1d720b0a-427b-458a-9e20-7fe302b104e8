import type { NangoAction, HarvestProjectTaskList } from '../models';
import { getHarvestAccountId } from './utils/harvestHelpers';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Lists task assignments for a specific project in Harvest.
 *
 * @param {NangoAction} nango - The Nango action object
 * @param {Object} input - The input parameters
 * @param {number} input.project_id - The ID of the project to get task assignments for
 * @returns {Promise<HarvestProjectTaskList | ActionError>} The list of task assignments or an error object
 */
export default async function runAction(
  nango: NangoAction,
  input: { project_id: number }
): Promise<HarvestProjectTaskList | ActionError> {
  try {
    if (!input?.project_id) {
      return {
        error: {
          status: 400,
          message: 'Missing required parameter: project_id is required',
        },
      };
    }

    const accountIdResult = await getHarvestAccountId(nango);

    if (typeof accountIdResult !== 'string' && 'error' in accountIdResult) {
      console.error('Failed to get Harvest Account ID:', accountIdResult.error.message);
      return accountIdResult;
    }
    const accountId = accountIdResult;

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/v2/projects/${input.project_id}/task_assignments`,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Error listing project tasks from Harvest:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while listing project tasks.';

    return {
      error: {
        status: status,
        message: `Failed to list project tasks: ${message}`,
      },
    };
  }
}
