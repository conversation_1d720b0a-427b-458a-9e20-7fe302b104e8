import type { NangoAction, HarvestCreateProjectInput, HarvestProject } from '../models';
import { getHarvestAccountId } from './utils/harvestHelpers';
type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: HarvestCreateProjectInput
): Promise<HarvestProject | NangoError> {
  try {
    const accountId = await getHarvestAccountId(nango);
    if (typeof accountId !== 'string') {
      return accountId as NangoError;
    }
    const payload: Record<string, any> = {};
    for (const key in input) {
      if (input[key as keyof HarvestCreateProjectInput] !== undefined) {
        payload[key] = input[key as keyof HarvestCreateProjectInput];
      }
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/v2/projects',
      data: payload,
      headers: {
        'Harvest-Account-Id': accountId,
        'Content-Type': 'application/json',
      },
      retries: 0,
    });

    return response.data;
  } catch (error: any) {
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while creating the project.';
    console.error('Error creating project in Harvest:', message, error.response?.data);
    return { error: { status, message } };
  }
}
