import type { NangoAction, HarvestUpdateTimeEntryInput, HarvestTimeEntry } from '../models';
import { getHarvestAccountId } from './utils/harvestHelpers';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: HarvestUpdateTimeEntryInput
): Promise<HarvestTimeEntry | ActionError> {
  try {
    if (!input.time_entry_id) {
      return {
        error: {
          status: 400,
          message: 'Missing required parameter: time_entry_id is required',
        },
      };
    }

    const payload: Record<string, any> = {};

    if (input.project_id !== undefined) {
      payload['project_id'] = input.project_id;
    }

    if (input.task_id !== undefined) {
      payload['task_id'] = input.task_id;
    }

    if (input.spent_date !== undefined) {
      payload['spent_date'] = input.spent_date;
    }

    if (input.hours !== undefined) {
      payload['hours'] = input.hours;
    }

    if (input.notes !== undefined) {
      payload['notes'] = input.notes;
    }

    const accountIdResult = await getHarvestAccountId(nango);

    if (typeof accountIdResult !== 'string' && 'error' in accountIdResult) {
      console.error('Failed to get Harvest Account ID:', accountIdResult.error.message);
      return accountIdResult;
    }
    const accountId = accountIdResult;

    const response = await nango.proxy({
      method: 'PATCH',
      endpoint: `/v2/time_entries/${input.time_entry_id}`,
      data: payload,
      headers: {
        'Content-Type': 'application/json',
        'Harvest-Account-Id': accountId,
      },
    });

    if (response.status !== 200 || !response.data) {
      const status = response.status || 500;
      const message = `Failed to update time entry. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('UPDATE_TIME_ENTRY_FAILED:', message);
      return { error: { status, message: `UPDATE_TIME_ENTRY_FAILED: ${message}` } };
    }

    const entry = response.data;

    return {
      id: entry.id,
      spent_date: entry.spent_date,
      hours: entry.hours,
      notes: entry.notes,
      is_locked: entry.is_locked,
      is_running: entry.is_running,
      is_billed: entry.is_billed,
      timer_started_at: entry.timer_started_at,
      started_time: entry.started_time,
      ended_time: entry.ended_time,
      user: {
        id: entry.user.id,
        name: entry.user.name,
        email: entry.user.email,
      },
      client: {
        id: entry.client.id,
        name: entry.client.name,
        is_active: true,
      },
      project: {
        id: entry.project.id,
        name: entry.project.name,
        code: entry.project.code,
        is_active: entry.project.is_active,
        is_billable: entry.project.is_billable,
      },
      task: {
        id: entry.task.id,
        name: entry.task.name,
        is_active: true,
        billable_by_default: entry.task.billable_by_default || false,
        created_at: entry.task.created_at || entry.created_at,
        updated_at: entry.task.updated_at || entry.updated_at,
      },
      created_at: entry.created_at,
      updated_at: entry.updated_at,
    };
  } catch (error: any) {
    console.error('Error updating time entry in Harvest:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while updating the time entry.';

    return {
      error: {
        status: status,
        message: `Failed to update time entry: ${message}`,
      },
    };
  }
}
