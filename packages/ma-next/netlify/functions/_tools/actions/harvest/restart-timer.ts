import type { HarvestTimeEntry, HarvestTimeEntryInput, NangoAction } from '../models';
import { getHarvestAccountId } from './utils/harvestHelpers';

type NangoError = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: HarvestTimeEntryInput
): Promise<HarvestTimeEntry | NangoError> {
  try {
    const timeEntryId = input.timeEntryId || input.id;

    const accountIdResult = await getHarvestAccountId(nango);

    if (typeof accountIdResult !== 'string' && 'error' in accountIdResult) {
      console.error('Failed to get Harvest Account ID:', accountIdResult.error.message);
      return accountIdResult;
    }

    const response = await nango.proxy({
      method: 'PATCH',
      endpoint: `/v2/time_entries/${timeEntryId}/restart`,
      headers: {
        'Harvest-Account-Id': accountIdResult,
      },
    });

    return response.data;
  } catch (error: any) {
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || error.message || 'Unknown error';
    console.error({ level: 'error', message: `Failed to stop time entry: ${message}` });
    return { error: { status, message } };
  }
}
