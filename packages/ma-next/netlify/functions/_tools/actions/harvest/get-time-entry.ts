import type { NangoAction, HarvestTimeEntryInput, HarvestTimeEntry } from '../models';
import { getHarvestAccountId } from './utils/harvestHelpers';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Gets a specific time entry by ID
 */
export default async function runAction(
  nango: NangoAction,
  input: HarvestTimeEntryInput
): Promise<HarvestTimeEntry | NangoError> {
  try {
    if (!input.timeEntryId) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Missing required parameter: timeEntryId is required',
        },
      };
    }

    const accountId = await getHarvestAccountId(nango);
    if (typeof accountId !== 'string') {
      return accountId as NangoError;
    }
    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/v2/time_entries/${input.timeEntryId}`,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    const entry = response.data;

    return {
      id: entry.id,
      spent_date: entry.spent_date,
      hours: entry.hours,
      notes: entry.notes,
      is_locked: entry.is_locked,
      is_running: entry.is_running,
      is_billed: entry.is_billed,
      timer_started_at: entry.timer_started_at,
      started_time: entry.started_time,
      ended_time: entry.ended_time,
      user: {
        id: entry.user.id,
        name: entry.user.name,
        email: entry.user.email,
      },
      client: {
        id: entry.client.id,
        name: entry.client.name,
        is_active: true,
      },
      project: {
        id: entry.project.id,
        name: entry.project.name,
        code: entry.project.code,
        is_active: entry.project.is_active,
        is_billable: entry.project.is_billable,
      },
      task: {
        id: entry.task.id,
        name: entry.task.name,
        is_active: true,
        billable_by_default: entry.task.billable_by_default || false,
        created_at: entry.task.created_at || entry.created_at,
        updated_at: entry.task.updated_at || entry.updated_at,
      },
      created_at: entry.created_at,
      updated_at: entry.updated_at,
    };
  } catch (error: any) {
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while fetching the time entry.';
    console.error('Error fetching time entry from Harvest:', error);
    return { error: { status, message } };
  }
}
